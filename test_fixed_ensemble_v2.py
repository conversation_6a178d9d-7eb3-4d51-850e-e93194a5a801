#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试修复后的方案2模型
================================================================================
使用修复后的集成模型进行v2数据验证
================================================================================
"""

import torch
import torch.nn as nn
import numpy as np
from pathlib import Path
import json
import time
from tqdm import tqdm

# 导入特征提取器
from ensemble_training import MultiFeatureExtractor


class FixedEnsembleModel(nn.Module):
    """修复后的集成模型 - 与修复脚本中的定义保持一致"""
    
    def __init__(self, input_dim, num_classes=5):
        super(FixedEnsembleModel, self).__init__()
        
        # 三个不同的子网络
        self.network1 = self._create_network(input_dim, num_classes, [512, 256, 128])
        self.network2 = self._create_network(input_dim, num_classes, [256, 128, 64])
        self.network3 = self._create_network(input_dim, num_classes, [1024, 512, 256])
        
        # 集成层
        self.ensemble_layer = nn.Linear(num_classes * 3, num_classes)
        
    def _create_network(self, input_dim, num_classes, hidden_dims):
        """创建子网络"""
        layers = []
        prev_dim = input_dim
        
        for hidden_dim in hidden_dims:
            layers.extend([
                nn.Linear(prev_dim, hidden_dim),
                nn.BatchNorm1d(hidden_dim),
                nn.ReLU(inplace=True),
                nn.Dropout(0.3)
            ])
            prev_dim = hidden_dim
        
        layers.append(nn.Linear(prev_dim, num_classes))
        return nn.Sequential(*layers)
    
    def forward(self, x):
        # 三个子网络的输出
        out1 = self.network1(x)
        out2 = self.network2(x)
        out3 = self.network3(x)
        
        # 拼接输出
        combined = torch.cat([out1, out2, out3], dim=1)
        
        # 集成预测
        final_output = self.ensemble_layer(combined)
        
        return final_output


class FixedV2Validator:
    """修复后的V2数据验证器"""
    
    def __init__(self):
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        self.class_names = ['belly_pain', 'burping', 'discomfort', 'hungry', 'tired']
        self.class_mapping = {name: idx for idx, name in enumerate(self.class_names)}
        
        # 加载模型和特征提取器
        self._load_model()
    
    def _load_model(self):
        """加载修复后的模型"""
        print("🔧 加载修复后的方案2模型...")
        
        # 初始化特征提取器
        self.feature_extractor = MultiFeatureExtractor()
        
        # 加载修复后的模型
        try:
            model_path = "ensemble_training_results/fixed_ensemble_v2_weights.pth"
            if Path(model_path).exists():
                # 先确定特征维度
                test_files = list(Path("Data/v2/hungry").glob("*.wav"))
                if test_files:
                    features = self.feature_extractor.extract_features(test_files[0], augment=False)
                    if features:
                        input_dim = len(features[0])
                        print(f"检测到特征维度: {input_dim}")
                        
                        # 创建模型并加载权重
                        self.model = FixedEnsembleModel(input_dim=input_dim, num_classes=5)
                        self.model.load_state_dict(torch.load(model_path, map_location=self.device))
                        self.model = self.model.to(self.device)
                        self.model.eval()
                        print("✅ 修复后的方案2模型加载成功")
                    else:
                        print("❌ 无法提取特征确定维度")
                        self.model = None
                else:
                    print("❌ 找不到测试文件")
                    self.model = None
            else:
                print("❌ 修复后的模型文件不存在")
                self.model = None
        except Exception as e:
            print(f"❌ 模型加载失败: {e}")
            self.model = None
    
    def _load_v2_data(self):
        """加载v2数据"""
        print("📊 加载Data/v2目录中的数据...")
        
        data_dir = Path("Data/v2")
        if not data_dir.exists():
            print("❌ Data/v2目录不存在")
            return {}
        
        data = {}
        total_files = 0
        
        for class_name in self.class_names:
            class_dir = data_dir / class_name
            if class_dir.exists():
                files = list(class_dir.glob("*.wav"))
                data[class_name] = files
                print(f"  {class_name}: {len(files)} 个文件")
                total_files += len(files)
            else:
                print(f"  {class_name}: 目录不存在")
                data[class_name] = []
        
        print(f"\n📊 总共找到 {total_files} 个测试文件")
        return data
    
    def _predict_single(self, audio_file):
        """预测单个音频文件"""
        try:
            # 提取特征
            features = self.feature_extractor.extract_features(audio_file, augment=False)
            if not features:
                return None
            
            # 转换为tensor
            feature_tensor = torch.FloatTensor(features[0]).unsqueeze(0).to(self.device)
            
            # 预测
            with torch.no_grad():
                output = self.model(feature_tensor)
                predicted = torch.argmax(output, dim=1).item()
            
            return predicted
        except Exception as e:
            print(f"预测失败 {audio_file}: {e}")
            return None
    
    def evaluate_model(self):
        """评估修复后的模型"""
        if self.model is None:
            print("❌ 模型未加载，无法评估")
            return None
        
        print("🧪 评估修复后的方案2 (集成学习)...")
        
        # 加载数据
        data = self._load_v2_data()
        if not data:
            return None
        
        results = {}
        total_correct = 0
        total_samples = 0
        
        for class_name, files in data.items():
            if not files:
                continue
            
            print(f"  测试 {class_name} ({len(files)} 个文件)...")
            
            correct = 0
            class_idx = self.class_mapping[class_name]
            
            for file_path in tqdm(files, desc=f"  {class_name}"):
                predicted = self._predict_single(file_path)
                if predicted is not None:
                    if predicted == class_idx:
                        correct += 1
                    total_samples += 1
            
            accuracy = correct / len(files) if files else 0
            results[class_name] = {
                'correct': correct,
                'total': len(files),
                'accuracy': accuracy
            }
            
            total_correct += correct
            print(f"    {class_name}: {accuracy:.3f} ({correct}/{len(files)})")
        
        # 计算总体准确率
        overall_accuracy = total_correct / total_samples if total_samples > 0 else 0
        results['overall'] = {
            'correct': total_correct,
            'total': total_samples,
            'accuracy': overall_accuracy
        }
        
        print(f"\n🏆 修复后的方案2总体准确率: {overall_accuracy:.3f} ({overall_accuracy*100:.1f}%)")
        
        return results
    
    def save_results(self, results):
        """保存结果"""
        if results is None:
            return
        
        output_file = "fixed_ensemble_v2_results.json"
        
        # 转换为可序列化的格式
        serializable_results = {}
        for key, value in results.items():
            serializable_results[key] = {
                'correct': int(value['correct']),
                'total': int(value['total']),
                'accuracy': float(value['accuracy'])
            }
        
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(serializable_results, f, indent=2, ensure_ascii=False)
        
        print(f"📋 详细结果已保存: {output_file}")


def main():
    """主函数"""
    print("🚀 测试修复后的方案2模型")
    print("=" * 60)
    
    # 创建验证器
    validator = FixedV2Validator()
    
    # 评估模型
    results = validator.evaluate_model()
    
    # 保存结果
    validator.save_results(results)
    
    if results:
        print("\n✅ 方案2修复验证完成！")
    else:
        print("\n❌ 方案2修复验证失败")


if __name__ == "__main__":
    main()
