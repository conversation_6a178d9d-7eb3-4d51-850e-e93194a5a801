#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复方案2模型的BatchNorm问题
================================================================================
重新创建和保存方案2模型，解决BatchNorm维度不匹配问题
================================================================================
"""

import torch
import torch.nn as nn
import numpy as np
from pathlib import Path
from ensemble_training import MultiFeatureExtractor, EnsembleModel

def fix_ensemble_model():
    """修复方案2模型"""
    print("🔧 修复方案2模型的BatchNorm问题...")
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    
    # 创建特征提取器测试实际维度
    feature_extractor = MultiFeatureExtractor()
    
    # 找一个测试文件确定特征维度
    test_files = []
    data_dirs = ['Data/belly_pain', 'Data/burping', 'Data/discomfort', 'Data/hungry', 'Data/tired']
    
    for data_dir in data_dirs:
        dir_path = Path(data_dir)
        if dir_path.exists():
            audio_files = list(dir_path.glob('*.wav'))
            if audio_files:
                test_files.append(audio_files[0])
                break
    
    if not test_files:
        print("❌ 没有找到测试文件")
        return
    
    # 确定实际特征维度
    test_features = feature_extractor.extract_features(test_files[0], augment=False)
    actual_dim = len(test_features[0])
    print(f"实际特征维度: {actual_dim}")
    
    # 创建新的模型架构
    print("🏗️ 创建新的模型架构...")
    new_model = EnsembleModel(input_dim=actual_dim, num_classes=5)
    new_model = new_model.to(device)
    
    # 尝试加载原始权重
    weights_path = "ensemble_training_results/best_ensemble_model.pth"
    if Path(weights_path).exists():
        print("📥 加载原始权重...")
        try:
            # 加载原始权重
            original_weights = torch.load(weights_path, map_location=device)
            
            # 尝试加载兼容的权重
            new_state_dict = new_model.state_dict()
            loaded_keys = []
            
            for key, value in original_weights.items():
                if key in new_state_dict:
                    if new_state_dict[key].shape == value.shape:
                        new_state_dict[key] = value
                        loaded_keys.append(key)
                    else:
                        print(f"⚠️ 跳过维度不匹配的权重: {key}")
                        print(f"   期望: {new_state_dict[key].shape}, 实际: {value.shape}")
            
            # 加载兼容的权重
            new_model.load_state_dict(new_state_dict)
            print(f"✅ 成功加载 {len(loaded_keys)} 个权重参数")
            
        except Exception as e:
            print(f"⚠️ 权重加载失败，使用随机初始化: {e}")
    else:
        print("⚠️ 原始权重文件不存在，使用随机初始化")
    
    # 设置为评估模式
    new_model.eval()
    
    # 测试模型
    print("🧪 测试修复后的模型...")
    try:
        test_input = torch.FloatTensor(test_features[0]).unsqueeze(0).to(device)
        with torch.no_grad():
            output = new_model(test_input)
            print(f"✅ 模型测试成功，输出形状: {output.shape}")
    except Exception as e:
        print(f"❌ 模型测试失败: {e}")
        return
    
    # 保存修复后的模型
    output_dir = Path("ensemble_training_results")
    output_dir.mkdir(exist_ok=True)
    
    # 保存完整模型
    fixed_model_path = output_dir / "fixed_ensemble_model.pth"
    torch.save(new_model, fixed_model_path)
    print(f"💾 修复后的完整模型已保存: {fixed_model_path}")
    
    # 保存权重
    fixed_weights_path = output_dir / "fixed_ensemble_weights.pth"
    torch.save(new_model.state_dict(), fixed_weights_path)
    print(f"💾 修复后的权重已保存: {fixed_weights_path}")
    
    return new_model

def test_fixed_model():
    """测试修复后的模型"""
    print("\n🧪 测试修复后的模型...")
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    
    # 加载修复后的模型
    fixed_model_path = "ensemble_training_results/fixed_ensemble_model.pth"
    if not Path(fixed_model_path).exists():
        print("❌ 修复后的模型文件不存在")
        return
    
    try:
        model = torch.load(fixed_model_path, map_location=device)
        model.eval()
        print("✅ 修复后的模型加载成功")
    except Exception as e:
        print(f"❌ 修复后的模型加载失败: {e}")
        return
    
    # 创建特征提取器
    feature_extractor = MultiFeatureExtractor()
    
    # 找测试文件
    test_files = []
    data_dirs = ['Data/v2/belly_pain', 'Data/v2/burping', 'Data/v2/discomfort', 'Data/v2/hungry', 'Data/v2/tired']
    
    for data_dir in data_dirs:
        dir_path = Path(data_dir)
        if dir_path.exists():
            audio_files = list(dir_path.glob('*.wav'))
            if audio_files:
                test_files.extend(audio_files[:2])  # 每个类别取2个文件
    
    if not test_files:
        print("❌ 没有找到v2测试文件")
        return
    
    print(f"找到 {len(test_files)} 个测试文件")
    
    # 测试预测
    success_count = 0
    for i, test_file in enumerate(test_files[:5]):  # 测试前5个文件
        try:
            # 提取特征
            features = feature_extractor.extract_features(test_file, augment=False)
            if features and len(features) > 0:
                features_tensor = torch.FloatTensor(features[0]).unsqueeze(0).to(device)
                
                # 预测
                with torch.no_grad():
                    outputs = model(features_tensor)
                    probabilities = torch.softmax(outputs, dim=1)
                    confidence, predicted = torch.max(probabilities, 1)
                
                print(f"✅ 文件 {i+1}: {test_file.name} -> 预测类别: {predicted.item()}, 置信度: {confidence.item():.3f}")
                success_count += 1
            else:
                print(f"❌ 文件 {i+1}: 特征提取失败")
                
        except Exception as e:
            print(f"❌ 文件 {i+1}: 预测失败 - {e}")
    
    print(f"\n📊 测试结果: {success_count}/{len(test_files[:5])} 个文件预测成功")
    
    if success_count == len(test_files[:5]):
        print("🎉 修复成功！模型可以正常预测")
        return True
    else:
        print("⚠️ 仍有问题需要进一步修复")
        return False

def main():
    """主函数"""
    print("🚀 修复方案2模型")
    print("=" * 60)
    
    # 修复模型
    fixed_model = fix_ensemble_model()
    
    if fixed_model is not None:
        # 测试修复后的模型
        success = test_fixed_model()
        
        if success:
            print("\n✅ 方案2模型修复完成！")
            print("现在可以使用修复后的模型进行v2数据验证")
        else:
            print("\n❌ 模型修复未完全成功，可能需要重新训练")
    else:
        print("\n❌ 模型修复失败")

if __name__ == "__main__":
    main()
