#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试特征维度问题
================================================================================
"""

import numpy as np
from pathlib import Path
from ensemble_training import MultiFeatureExtractor

def test_feature_dimensions():
    """测试特征维度"""
    print("🔧 测试MultiFeatureExtractor的特征维度...")
    
    # 创建特征提取器
    extractor = MultiFeatureExtractor()
    
    # 找一个测试音频文件
    test_files = []
    data_dirs = ['Data/belly_pain', 'Data/burping', 'Data/discomfort', 'Data/hungry', 'Data/tired']
    
    for data_dir in data_dirs:
        dir_path = Path(data_dir)
        if dir_path.exists():
            audio_files = list(dir_path.glob('*.wav'))
            if audio_files:
                test_files.extend(audio_files[:2])  # 取前2个文件
    
    if not test_files:
        print("❌ 没有找到测试音频文件")
        return
    
    print(f"找到 {len(test_files)} 个测试文件")
    
    # 测试特征提取
    for i, test_file in enumerate(test_files[:3]):  # 只测试前3个
        print(f"\n测试文件 {i+1}: {test_file.name}")
        
        try:
            # 不增强的特征
            features_no_aug = extractor.extract_features(test_file, augment=False)
            print(f"  无增强特征数量: {len(features_no_aug)}")
            if features_no_aug:
                print(f"  特征维度: {features_no_aug[0].shape}")
                print(f"  特征长度: {len(features_no_aug[0])}")
            
            # 增强的特征
            features_aug = extractor.extract_features(test_file, augment=True)
            print(f"  增强特征数量: {len(features_aug)}")
            if features_aug:
                print(f"  增强特征维度: {features_aug[0].shape}")
                print(f"  增强特征长度: {len(features_aug[0])}")
                
        except Exception as e:
            print(f"  ❌ 特征提取失败: {e}")
    
    # 计算实际特征维度
    print(f"\n🔍 分析特征组成:")
    try:
        # 使用第一个文件进行详细分析
        test_file = test_files[0]
        print(f"分析文件: {test_file.name}")
        
        import librosa
        audio, sr = librosa.load(test_file, sr=16000)
        target_length = 16000 * 3
        if len(audio) < target_length:
            audio = np.pad(audio, (0, target_length - len(audio)), mode='constant')
        else:
            audio = audio[:target_length]
        
        # 分别计算各部分特征
        spectral_features = extractor.extract_spectral_features(audio, sr)
        temporal_features = extractor.extract_temporal_features(audio, sr)
        
        print(f"  频谱特征维度: {spectral_features.shape} ({len(spectral_features)})")
        print(f"  时域特征维度: {temporal_features.shape} ({len(temporal_features)})")
        print(f"  总特征维度: {len(spectral_features) + len(temporal_features)}")
        
        # 详细分析频谱特征
        print(f"\n  频谱特征详细分析:")
        mel_spec = librosa.feature.melspectrogram(y=audio, sr=sr, n_mels=128)
        mfcc = librosa.feature.mfcc(y=audio, sr=sr, n_mfcc=20)
        
        print(f"    Mel频谱: {mel_spec.shape} -> 4种统计量 * 128 = {4 * 128}")
        print(f"    MFCC: {mfcc.shape} -> 4种统计量 * 20 = {4 * 20}")
        print(f"    频谱特征总计: {4 * 128 + 4 * 20} = {4 * 148}")
        
        print(f"\n  时域特征详细分析:")
        print(f"    零交叉率: 2 (均值+标准差)")
        print(f"    RMS能量: 2 (均值+标准差)")
        print(f"    谱质心: 2 (均值+标准差)")
        print(f"    谱带宽: 2 (均值+标准差)")
        print(f"    谱滚降: 2 (均值+标准差)")
        print(f"    时域特征总计: 10")
        
        expected_total = 4 * 148 + 10
        print(f"\n  预期总特征维度: {expected_total}")
        
    except Exception as e:
        print(f"❌ 详细分析失败: {e}")

if __name__ == "__main__":
    test_feature_dimensions()
