#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试原始方案2模型
================================================================================
尝试直接加载原始模型并分析问题
================================================================================
"""

import torch
import torch.nn as nn
import numpy as np
from pathlib import Path
from ensemble_training import MultiFeatureExtractor, EnsembleModel


def debug_original_model():
    """调试原始模型"""
    print("🔍 调试原始方案2模型")
    print("=" * 60)
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"使用设备: {device}")
    
    # 创建特征提取器
    feature_extractor = MultiFeatureExtractor()
    
    # 获取测试文件
    test_files = list(Path("Data/v2/hungry").glob("*.wav"))
    if not test_files:
        print("❌ 找不到测试文件")
        return
    
    test_file = test_files[0]
    print(f"使用测试文件: {test_file.name}")
    
    # 提取特征
    try:
        features = feature_extractor.extract_features(test_file, augment=False)
        if features:
            feature_dim = len(features[0])
            print(f"✅ 特征维度: {feature_dim}")
            test_feature = torch.FloatTensor(features[0]).unsqueeze(0).to(device)
        else:
            print("❌ 特征提取失败")
            return
    except Exception as e:
        print(f"❌ 特征提取错误: {e}")
        return
    
    # 尝试加载不同的模型文件
    model_files = [
        "ensemble_training_results/ensemble_full_model.pth",
        "ensemble_training_results/best_ensemble_model.pth"
    ]
    
    for model_path in model_files:
        print(f"\n🔧 尝试加载: {model_path}")
        
        if not Path(model_path).exists():
            print(f"❌ 文件不存在")
            continue
        
        try:
            if "full_model" in model_path:
                # 加载完整模型
                model = torch.load(model_path, map_location=device)
                print(f"✅ 完整模型加载成功")
            else:
                # 加载权重
                model = EnsembleModel(input_dim=feature_dim, num_classes=5)
                model.load_state_dict(torch.load(model_path, map_location=device))
                model = model.to(device)
                print(f"✅ 权重模型加载成功")
            
            model.eval()
            
            # 测试预测
            with torch.no_grad():
                output = model(test_feature)
                predicted = torch.argmax(output, dim=1).item()
                probabilities = torch.softmax(output, dim=1)[0]
                
            print(f"✅ 预测成功")
            print(f"   预测类别: {predicted}")
            print(f"   概率分布: {probabilities.cpu().numpy()}")
            
            # 测试多个文件
            print(f"\n🧪 测试多个文件...")
            class_names = ['belly_pain', 'burping', 'discomfort', 'hungry', 'tired']
            
            for class_name in class_names:
                class_dir = Path(f"Data/v2/{class_name}")
                if class_dir.exists():
                    files = list(class_dir.glob("*.wav"))[:3]  # 只测试前3个
                    correct = 0
                    total = len(files)
                    
                    for file_path in files:
                        try:
                            features = feature_extractor.extract_features(file_path, augment=False)
                            if features:
                                feature_tensor = torch.FloatTensor(features[0]).unsqueeze(0).to(device)
                                with torch.no_grad():
                                    output = model(feature_tensor)
                                    predicted = torch.argmax(output, dim=1).item()
                                
                                expected_class = class_names.index(class_name)
                                if predicted == expected_class:
                                    correct += 1
                                
                                print(f"   {file_path.name}: 预测={class_names[predicted]}, 期望={class_name}")
                        except Exception as e:
                            print(f"   ❌ {file_path.name}: {e}")
                    
                    accuracy = correct / total if total > 0 else 0
                    print(f"   {class_name}: {accuracy:.3f} ({correct}/{total})")
            
            return model
            
        except Exception as e:
            print(f"❌ 模型加载失败: {e}")
            import traceback
            traceback.print_exc()
    
    return None


def analyze_model_architecture(model):
    """分析模型架构"""
    print(f"\n🔍 分析模型架构:")
    
    print(f"模型类型: {type(model)}")
    print(f"模型参数数量: {sum(p.numel() for p in model.parameters()):,}")
    
    # 打印模型结构
    print(f"\n模型结构:")
    for name, module in model.named_modules():
        if len(list(module.children())) == 0:  # 叶子节点
            print(f"  {name}: {module}")


def main():
    """主函数"""
    model = debug_original_model()
    
    if model is not None:
        analyze_model_architecture(model)
        print(f"\n✅ 原始模型调试完成")
    else:
        print(f"\n❌ 原始模型调试失败")


if __name__ == "__main__":
    main()
