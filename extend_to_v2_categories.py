#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
扩展方案1和方案2到V2的所有类别
================================================================================
基于V2目录的9个类别重新训练模型
================================================================================
"""

import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import Dataset, DataLoader
import numpy as np
from pathlib import Path
import json
import time
from tqdm import tqdm
from collections import Counter
from sklearn.model_selection import train_test_split
import librosa

# 导入原有的特征提取器
from stable_training_v1 import StableFeatureExtractor
from ensemble_training import MultiFeatureExtractor


class ExtendedV2Dataset(Dataset):
    """扩展的V2数据集 - 支持9个类别"""
    
    def __init__(self, data_dir, feature_extractor, split='train', test_size=0.2):
        self.feature_extractor = feature_extractor
        
        # 扩展的标签映射 - 9个类别
        self.label_map = {
            'belly_pain': 0,
            'burping': 1, 
            'cold_hot': 2,
            'discomfort': 3,
            'hungry': 4,
            'lonely': 5,
            'scared': 6,
            'tired': 7,
            'unknown': 8
        }
        
        self.class_names = list(self.label_map.keys())
        self.num_classes = len(self.class_names)
        
        # 收集V2数据
        self.data = self._collect_v2_data(data_dir)
        
        # 划分数据集
        if len(self.data) > 0:
            train_data, test_data = train_test_split(
                self.data, test_size=test_size, random_state=42, 
                stratify=[item[1] for item in self.data]
            )
            
            if split == 'train':
                self.data = train_data
            else:
                self.data = test_data
        
        print(f"{split} 集大小: {len(self.data)}")
        if len(self.data) > 0:
            label_counts = Counter([item[1] for item in self.data])
            print(f"标签分布: {dict(label_counts)}")
    
    def _collect_v2_data(self, data_dir):
        """收集V2目录下的所有数据"""
        data = []
        data_path = Path(data_dir)
        
        print("📊 收集V2数据...")
        for class_name in self.class_names:
            class_dir = data_path / class_name
            if class_dir.exists():
                audio_files = list(class_dir.glob("*.wav"))
                label = self.label_map[class_name]
                
                for audio_file in audio_files:
                    data.append((str(audio_file), label))
                
                print(f"  {class_name}: {len(audio_files)} 个文件")
            else:
                print(f"  {class_name}: 目录不存在")
        
        print(f"总共收集到 {len(data)} 个样本")
        return data
    
    def __len__(self):
        return len(self.data)
    
    def __getitem__(self, idx):
        audio_path, label = self.data[idx]
        
        # 提取特征
        if isinstance(self.feature_extractor, MultiFeatureExtractor):
            # MultiFeatureExtractor支持augment参数
            features = self.feature_extractor.extract_features(audio_path, augment=False)
            if features and len(features) > 0:
                feature_vector = features[0]
            else:
                feature_vector = np.zeros(602, dtype=np.float32)  # MultiFeatureExtractor默认维度
        else:
            # StableFeatureExtractor不支持augment参数
            feature_vector = self.feature_extractor.extract_features(audio_path)
            if feature_vector is None:
                feature_vector = np.zeros(632, dtype=np.float32)  # StableFeatureExtractor默认维度
        
        return torch.FloatTensor(feature_vector), torch.LongTensor([label])


class ExtendedStableModel(nn.Module):
    """扩展的方案1模型 - 支持9个类别"""
    
    def __init__(self, input_dim, num_classes=9):
        super(ExtendedStableModel, self).__init__()
        
        self.features = nn.Sequential(
            nn.Linear(input_dim, 512),
            nn.BatchNorm1d(512),
            nn.ReLU(inplace=True),
            nn.Dropout(0.3),
            
            nn.Linear(512, 256),
            nn.BatchNorm1d(256),
            nn.ReLU(inplace=True),
            nn.Dropout(0.3),
            
            nn.Linear(256, 128),
            nn.BatchNorm1d(128),
            nn.ReLU(inplace=True),
            nn.Dropout(0.2),
        )
        
        self.classifier = nn.Linear(128, num_classes)
        
        # 初始化权重
        self._initialize_weights()
    
    def _initialize_weights(self):
        for m in self.modules():
            if isinstance(m, nn.Linear):
                nn.init.xavier_uniform_(m.weight)
                if m.bias is not None:
                    nn.init.constant_(m.bias, 0)
            elif isinstance(m, nn.BatchNorm1d):
                nn.init.constant_(m.weight, 1)
                nn.init.constant_(m.bias, 0)
    
    def forward(self, x):
        x = self.features(x)
        x = self.classifier(x)
        return x


class ExtendedEnsembleModel(nn.Module):
    """扩展的方案2集成模型 - 支持9个类别"""
    
    def __init__(self, input_dim, num_classes=9):
        super(ExtendedEnsembleModel, self).__init__()
        
        # 三个不同的子网络
        self.network1 = self._create_network(input_dim, num_classes, [512, 256, 128])
        self.network2 = self._create_network(input_dim, num_classes, [256, 128, 64])
        self.network3 = self._create_network(input_dim, num_classes, [1024, 512, 256])
        
        # 集成层
        self.ensemble_layer = nn.Linear(num_classes * 3, num_classes)
        
        # 初始化权重
        self._initialize_weights()
    
    def _create_network(self, input_dim, num_classes, hidden_dims):
        """创建子网络"""
        layers = []
        prev_dim = input_dim
        
        for hidden_dim in hidden_dims:
            layers.extend([
                nn.Linear(prev_dim, hidden_dim),
                nn.BatchNorm1d(hidden_dim),
                nn.ReLU(inplace=True),
                nn.Dropout(0.3)
            ])
            prev_dim = hidden_dim
        
        layers.append(nn.Linear(prev_dim, num_classes))
        return nn.Sequential(*layers)
    
    def _initialize_weights(self):
        for m in self.modules():
            if isinstance(m, nn.Linear):
                nn.init.xavier_uniform_(m.weight)
                if m.bias is not None:
                    nn.init.constant_(m.bias, 0)
            elif isinstance(m, nn.BatchNorm1d):
                nn.init.constant_(m.weight, 1)
                nn.init.constant_(m.bias, 0)
    
    def forward(self, x):
        # 三个子网络的输出
        out1 = self.network1(x)
        out2 = self.network2(x)
        out3 = self.network3(x)
        
        # 拼接输出
        combined = torch.cat([out1, out2, out3], dim=1)
        
        # 集成预测
        final_output = self.ensemble_layer(combined)
        
        return final_output


def check_v2_data_distribution():
    """检查V2数据分布"""
    print("🔍 检查V2数据分布...")
    
    v2_path = Path("Data/v2")
    if not v2_path.exists():
        print("❌ Data/v2 目录不存在")
        return None
    
    class_counts = {}
    total_files = 0
    
    for class_dir in v2_path.iterdir():
        if class_dir.is_dir():
            audio_files = list(class_dir.glob("*.wav"))
            class_counts[class_dir.name] = len(audio_files)
            total_files += len(audio_files)
    
    print(f"\n📊 V2数据分布:")
    for class_name, count in sorted(class_counts.items()):
        percentage = (count / total_files) * 100 if total_files > 0 else 0
        print(f"  {class_name}: {count} 个文件 ({percentage:.1f}%)")
    
    print(f"\n总计: {total_files} 个文件")
    return class_counts


def train_extended_model(model_type='stable'):
    """训练扩展模型"""
    print(f"🚀 训练扩展的{model_type}模型 (9个类别)")
    print("=" * 60)
    
    # 检查数据分布
    data_dist = check_v2_data_distribution()
    if data_dist is None:
        return
    
    # 设备选择
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"使用设备: {device}")
    
    # 选择特征提取器并确定特征维度
    if model_type == 'stable':
        feature_extractor = StableFeatureExtractor()
        # 测试获取实际特征维度
        test_files = list(Path("Data/v2/hungry").glob("*.wav"))
        if test_files:
            test_features = feature_extractor.extract_features(test_files[0])
            input_dim = len(test_features) if test_features is not None else 632
        else:
            input_dim = 632  # StableFeatureExtractor默认维度
    else:  # ensemble
        feature_extractor = MultiFeatureExtractor()
        # 测试获取实际特征维度
        test_files = list(Path("Data/v2/hungry").glob("*.wav"))
        if test_files:
            test_features = feature_extractor.extract_features(test_files[0], augment=False)
            input_dim = len(test_features[0]) if test_features and len(test_features) > 0 else 602
        else:
            input_dim = 602  # MultiFeatureExtractor默认维度

    print(f"使用特征维度: {input_dim}")
    
    # 创建数据集
    train_dataset = ExtendedV2Dataset("Data/v2", feature_extractor, split='train')
    test_dataset = ExtendedV2Dataset("Data/v2", feature_extractor, split='test')
    
    if len(train_dataset) == 0:
        print("❌ 训练数据为空")
        return
    
    # 创建数据加载器
    train_loader = DataLoader(train_dataset, batch_size=32, shuffle=True)
    test_loader = DataLoader(test_dataset, batch_size=32, shuffle=False)
    
    # 创建模型
    if model_type == 'stable':
        model = ExtendedStableModel(input_dim, num_classes=9)
    else:
        model = ExtendedEnsembleModel(input_dim, num_classes=9)
    
    model = model.to(device)
    
    # 计算类别权重来处理数据不平衡
    class_counts = [0] * 9
    for _, label in train_dataset.data:
        class_counts[label] += 1

    # 计算权重 (反比例)
    total_samples = sum(class_counts)
    class_weights = [total_samples / (9 * count) if count > 0 else 0 for count in class_counts]
    class_weights = torch.FloatTensor(class_weights).to(device)

    print(f"类别权重: {class_weights.cpu().numpy()}")

    # 损失函数和优化器 (使用加权损失)
    criterion = nn.CrossEntropyLoss(weight=class_weights)
    optimizer = optim.Adam(model.parameters(), lr=0.001, weight_decay=1e-4)
    scheduler = optim.lr_scheduler.StepLR(optimizer, step_size=10, gamma=0.7)
    
    # 训练参数
    num_epochs = 50
    best_accuracy = 0.0
    
    print(f"\n🎯 开始训练 ({num_epochs} epochs)...")
    
    for epoch in range(num_epochs):
        # 训练阶段
        model.train()
        train_loss = 0.0
        train_correct = 0
        train_total = 0
        
        for batch_idx, (data, target) in enumerate(tqdm(train_loader, desc=f"Epoch {epoch+1}")):
            data, target = data.to(device), target.squeeze().to(device)
            
            optimizer.zero_grad()
            output = model(data)
            loss = criterion(output, target)
            loss.backward()
            optimizer.step()
            
            train_loss += loss.item()
            _, predicted = torch.max(output.data, 1)
            train_total += target.size(0)
            train_correct += (predicted == target).sum().item()
        
        # 验证阶段
        model.eval()
        test_loss = 0.0
        test_correct = 0
        test_total = 0
        
        with torch.no_grad():
            for data, target in test_loader:
                data, target = data.to(device), target.squeeze().to(device)
                output = model(data)
                loss = criterion(output, target)
                
                test_loss += loss.item()
                _, predicted = torch.max(output.data, 1)
                test_total += target.size(0)
                test_correct += (predicted == target).sum().item()
        
        # 计算准确率
        train_accuracy = 100. * train_correct / train_total
        test_accuracy = 100. * test_correct / test_total
        
        print(f"Epoch {epoch+1}/{num_epochs}:")
        print(f"  训练: Loss={train_loss/len(train_loader):.4f}, Acc={train_accuracy:.2f}%")
        print(f"  验证: Loss={test_loss/len(test_loader):.4f}, Acc={test_accuracy:.2f}%")
        
        # 保存最佳模型
        if test_accuracy > best_accuracy:
            best_accuracy = test_accuracy
            output_dir = Path(f"extended_{model_type}_v2_results")
            output_dir.mkdir(exist_ok=True)
            
            torch.save(model, output_dir / f"best_extended_{model_type}_model.pth")
            torch.save(model.state_dict(), output_dir / f"best_extended_{model_type}_weights.pth")
            print(f"  💾 保存最佳模型 (准确率: {best_accuracy:.2f}%)")
        
        scheduler.step()
    
    print(f"\n✅ 训练完成！最佳验证准确率: {best_accuracy:.2f}%")
    return model


def main():
    """主函数"""
    print("🚀 扩展方案到V2的所有类别")
    print("=" * 60)
    
    # 检查V2数据
    check_v2_data_distribution()
    
    # 询问用户选择
    print("\n请选择要训练的模型:")
    print("1. 扩展方案1 (稳定训练)")
    print("2. 扩展方案2 (集成学习)")
    print("3. 两个都训练")
    
    choice = input("请输入选择 (1/2/3): ").strip()
    
    if choice == '1':
        train_extended_model('stable')
    elif choice == '2':
        train_extended_model('ensemble')
    elif choice == '3':
        print("\n训练扩展方案1...")
        train_extended_model('stable')
        print("\n训练扩展方案2...")
        train_extended_model('ensemble')
    else:
        print("无效选择")


if __name__ == "__main__":
    main()
