#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
使用Data/v2目录验证两个方案的真实准确率
================================================================================
使用v2目录中分类好的音频文件验证方案1和方案2的实际性能
================================================================================
"""

import torch
import torch.nn as nn
import numpy as np
from pathlib import Path
import json
import time
from tqdm import tqdm
from collections import Counter
import matplotlib.pyplot as plt
import seaborn as sns

# 导入两个方案的模块
from stable_training_v1 import StableFeatureExtractor, StableDeepInfantModel
from ensemble_training import MultiFeatureExtractor, EnsembleModel

class V2DataValidator:
    """V2数据验证器"""
    
    def __init__(self):
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        
        # 我们训练的5个类别映射
        self.trained_classes = {
            'belly_pain': 0,
            'burping': 1,
            'discomfort': 2,
            'hungry': 3,
            'tired': 4
        }
        
        self.class_names_cn = {
            'belly_pain': '腹痛',
            'burping': '打嗝',
            'discomfort': '不适',
            'hungry': '饥饿',
            'tired': '疲倦'
        }
        
        # 加载模型
        self._load_models()
    
    def _load_models(self):
        """加载两个方案的模型"""
        print("🔧 加载训练好的模型...")

        # 初始化特征提取器
        self.v1_feature_extractor = StableFeatureExtractor()
        self.v2_feature_extractor = MultiFeatureExtractor()

        # 加载方案1模型
        try:
            # 先创建模型架构，然后加载权重
            self.v1_model = StableDeepInfantModel(input_dim=632, num_classes=5)
            v1_weights_path = "stable_training_v1_results/best_stable_v1_model.pth"
            if Path(v1_weights_path).exists():
                self.v1_model.load_state_dict(torch.load(v1_weights_path, map_location=self.device))
                self.v1_model = self.v1_model.to(self.device)
                self.v1_model.eval()
                print("✅ 方案1模型加载成功")
            else:
                print("❌ 方案1模型权重文件不存在")
                self.v1_model = None
        except Exception as e:
            print(f"❌ 方案1模型加载失败: {e}")
            self.v1_model = None

        # 加载方案2模型
        try:
            # 直接使用原始的完整模型（已验证可以正常工作）
            v2_full_path = "ensemble_training_results/ensemble_full_model.pth"
            if Path(v2_full_path).exists():
                self.v2_model = torch.load(v2_full_path, map_location=self.device)
                self.v2_model.eval()
                print("✅ 方案2原始完整模型加载成功")
            else:
                print("❌ 方案2模型文件不存在")
                self.v2_model = None
        except Exception as e:
            print(f"❌ 方案2模型加载失败: {e}")
            self.v2_model = None
    
    def _load_v2_data(self):
        """加载v2目录中的数据"""
        print("\n📊 加载Data/v2目录中的数据...")
        
        v2_data = {}
        v2_dir = Path("Data/v2")
        
        for class_name in self.trained_classes.keys():
            class_dir = v2_dir / class_name
            if class_dir.exists():
                audio_files = list(class_dir.glob('*.wav'))
                v2_data[class_name] = audio_files
                print(f"  {self.class_names_cn[class_name]}: {len(audio_files)} 个文件")
            else:
                v2_data[class_name] = []
                print(f"  {self.class_names_cn[class_name]}: 目录不存在")
        
        return v2_data
    
    def _predict_single(self, model, feature_extractor, audio_file):
        """预测单个音频文件"""
        try:
            # 提取特征（根据特征提取器类型决定是否使用augment参数）
            if hasattr(feature_extractor, 'extract_features'):
                # 检查是否支持augment参数
                import inspect
                sig = inspect.signature(feature_extractor.extract_features)
                if 'augment' in sig.parameters:
                    features = feature_extractor.extract_features(audio_file, augment=False)
                else:
                    features = feature_extractor.extract_features(audio_file)
            else:
                return None, 0.0

            if features is None:
                return None, 0.0

            # 处理不同的特征格式
            if isinstance(features, list) and len(features) > 0:
                # MultiFeatureExtractor返回列表
                feature_vector = features[0]
            elif isinstance(features, (list, tuple)) and len(features) == 0:
                return None, 0.0
            else:
                # StableFeatureExtractor直接返回特征向量
                feature_vector = features

            # 预测
            features_tensor = torch.FloatTensor(feature_vector).unsqueeze(0).to(self.device)
            
            with torch.no_grad():
                outputs = model(features_tensor)
                probabilities = torch.softmax(outputs, dim=1)
                confidence, predicted = torch.max(probabilities, 1)
            
            return predicted.item(), confidence.item()
            
        except Exception as e:
            print(f"预测失败 {audio_file}: {e}")
            return None, 0.0
    
    def _evaluate_model(self, model, feature_extractor, v2_data, model_name):
        """评估单个模型"""
        print(f"\n🧪 评估{model_name}...")
        
        if model is None:
            print(f"❌ {model_name}模型未加载，跳过评估")
            return None
        
        results = {
            'model_name': model_name,
            'class_results': {},
            'overall_accuracy': 0.0,
            'total_samples': 0,
            'correct_predictions': 0,
            'detailed_predictions': []
        }
        
        total_correct = 0
        total_samples = 0
        
        for class_name, audio_files in v2_data.items():
            if not audio_files:
                continue
            
            class_correct = 0
            class_total = len(audio_files)
            expected_label = self.trained_classes[class_name]
            
            print(f"  测试 {self.class_names_cn[class_name]} ({class_total} 个文件)...")
            
            for audio_file in tqdm(audio_files, desc=f"  {class_name}", leave=False):
                predicted_label, confidence = self._predict_single(model, feature_extractor, audio_file)
                
                if predicted_label is not None:
                    is_correct = (predicted_label == expected_label)
                    if is_correct:
                        class_correct += 1
                        total_correct += 1
                    
                    total_samples += 1
                    
                    # 记录详细预测结果
                    results['detailed_predictions'].append({
                        'file': str(audio_file),
                        'true_class': class_name,
                        'true_label': expected_label,
                        'predicted_label': predicted_label,
                        'confidence': confidence,
                        'correct': is_correct
                    })
            
            # 计算类别准确率
            class_accuracy = class_correct / class_total if class_total > 0 else 0
            results['class_results'][class_name] = {
                'accuracy': class_accuracy,
                'correct': class_correct,
                'total': class_total,
                'cn_name': self.class_names_cn[class_name]
            }
            
            print(f"    {self.class_names_cn[class_name]}: {class_accuracy:.3f} ({class_correct}/{class_total})")
        
        # 计算总体准确率
        results['overall_accuracy'] = total_correct / total_samples if total_samples > 0 else 0
        results['total_samples'] = total_samples
        results['correct_predictions'] = total_correct
        
        return results
    
    def validate_both_models(self):
        """验证两个方案的模型"""
        print("🚀 使用Data/v2验证两个方案的真实准确率")
        print("=" * 80)
        
        # 加载v2数据
        v2_data = self._load_v2_data()
        
        # 检查是否有数据
        total_files = sum(len(files) for files in v2_data.values())
        if total_files == 0:
            print("❌ 没有找到v2数据文件")
            return
        
        print(f"\n📊 总共找到 {total_files} 个测试文件")
        
        # 评估方案1
        v1_results = self._evaluate_model(
            self.v1_model, self.v1_feature_extractor, v2_data, "方案1 (GPU稳定训练)"
        )
        
        # 评估方案2
        v2_results = self._evaluate_model(
            self.v2_model, self.v2_feature_extractor, v2_data, "方案2 (集成学习)"
        )
        
        # 显示对比结果
        self._display_comparison(v1_results, v2_results)
        
        # 保存详细结果
        self._save_results(v1_results, v2_results)
        
        return v1_results, v2_results
    
    def _display_comparison(self, v1_results, v2_results):
        """显示对比结果"""
        print(f"\n{'='*80}")
        print("🏆 V2数据验证结果对比")
        print("=" * 80)
        
        if v1_results and v2_results:
            print(f"{'方案':<20} {'总体准确率':<15} {'测试样本数':<10}")
            print("-" * 50)
            print(f"{'方案1 (GPU稳定训练)':<20} {v1_results['overall_accuracy']:.3f} ({v1_results['overall_accuracy']*100:.1f}%){'':<5} {v1_results['total_samples']}")
            print(f"{'方案2 (集成学习)':<20} {v2_results['overall_accuracy']:.3f} ({v2_results['overall_accuracy']*100:.1f}%){'':<5} {v2_results['total_samples']}")
            
            print(f"\n📊 各类别准确率对比:")
            print(f"{'类别':<10} {'方案1':<15} {'方案2':<15}")
            print("-" * 45)
            
            for class_name in self.trained_classes.keys():
                cn_name = self.class_names_cn[class_name]
                v1_acc = v1_results['class_results'].get(class_name, {}).get('accuracy', 0) * 100
                v2_acc = v2_results['class_results'].get(class_name, {}).get('accuracy', 0) * 100
                print(f"{cn_name:<10} {v1_acc:.1f}%{'':<10} {v2_acc:.1f}%")
            
            # 计算均衡性
            v1_accs = [v1_results['class_results'].get(c, {}).get('accuracy', 0) * 100 for c in self.trained_classes.keys()]
            v2_accs = [v2_results['class_results'].get(c, {}).get('accuracy', 0) * 100 for c in self.trained_classes.keys()]
            
            v1_std = np.std(v1_accs)
            v2_std = np.std(v2_accs)
            
            print(f"\n🎯 均衡性分析:")
            print(f"方案1类别间标准差: {v1_std:.1f}% (越小越均衡)")
            print(f"方案2类别间标准差: {v2_std:.1f}% (越小越均衡)")
            
            # 与之前训练结果对比
            print(f"\n📈 与训练时结果对比:")
            print(f"方案1: 训练时74.6% vs V2验证{v1_results['overall_accuracy']*100:.1f}%")
            print(f"方案2: 训练时90.2% vs V2验证{v2_results['overall_accuracy']*100:.1f}%")
            
        elif v1_results:
            print(f"方案1 (GPU稳定训练): {v1_results['overall_accuracy']:.3f} ({v1_results['overall_accuracy']*100:.1f}%)")
        elif v2_results:
            print(f"方案2 (集成学习): {v2_results['overall_accuracy']:.3f} ({v2_results['overall_accuracy']*100:.1f}%)")
        else:
            print("❌ 没有可用的验证结果")
    
    def _save_results(self, v1_results, v2_results):
        """保存验证结果"""
        validation_results = {
            'validation_dataset': 'Data/v2',
            'validation_time': time.strftime('%Y-%m-%d %H:%M:%S'),
            'v1_results': v1_results,
            'v2_results': v2_results
        }
        
        results_path = "v2_validation_results.json"
        with open(results_path, 'w', encoding='utf-8') as f:
            json.dump(validation_results, f, indent=2, ensure_ascii=False, default=str)
        
        print(f"\n📋 详细验证结果已保存: {results_path}")

def main():
    """主函数"""
    validator = V2DataValidator()
    validator.validate_both_models()

if __name__ == "__main__":
    main()
