#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
完全修复方案2的模型问题
================================================================================
重新构建兼容的集成模型，解决特征维度和BatchNorm问题
================================================================================
"""

import torch
import torch.nn as nn
import numpy as np
from pathlib import Path
import json
from tqdm import tqdm
import librosa

# 导入特征提取器
from ensemble_training import MultiFeatureExtractor


class FixedEnsembleModel(nn.Module):
    """修复后的集成模型 - 确保与特征提取器兼容"""
    
    def __init__(self, input_dim, num_classes=5):
        super(FixedEnsembleModel, self).__init__()
        
        print(f"🔧 创建修复后的集成模型，输入维度: {input_dim}")
        
        # 三个不同的子网络
        self.network1 = self._create_network(input_dim, num_classes, [512, 256, 128])
        self.network2 = self._create_network(input_dim, num_classes, [256, 128, 64])
        self.network3 = self._create_network(input_dim, num_classes, [1024, 512, 256])
        
        # 集成层
        self.ensemble_layer = nn.Linear(num_classes * 3, num_classes)
        
        # 初始化权重
        self._initialize_weights()
        
    def _create_network(self, input_dim, num_classes, hidden_dims):
        """创建子网络"""
        layers = []
        prev_dim = input_dim
        
        for hidden_dim in hidden_dims:
            layers.extend([
                nn.Linear(prev_dim, hidden_dim),
                nn.BatchNorm1d(hidden_dim),
                nn.ReLU(inplace=True),
                nn.Dropout(0.3)
            ])
            prev_dim = hidden_dim
        
        layers.append(nn.Linear(prev_dim, num_classes))
        return nn.Sequential(*layers)
    
    def _initialize_weights(self):
        """初始化权重"""
        for m in self.modules():
            if isinstance(m, nn.Linear):
                nn.init.xavier_uniform_(m.weight)
                if m.bias is not None:
                    nn.init.constant_(m.bias, 0)
            elif isinstance(m, nn.BatchNorm1d):
                nn.init.constant_(m.weight, 1)
                nn.init.constant_(m.bias, 0)
    
    def forward(self, x):
        # 三个子网络的输出
        out1 = self.network1(x)
        out2 = self.network2(x)
        out3 = self.network3(x)
        
        # 拼接输出
        combined = torch.cat([out1, out2, out3], dim=1)
        
        # 集成预测
        final_output = self.ensemble_layer(combined)
        
        return final_output


def get_correct_feature_dimension():
    """获取正确的特征维度"""
    print("🔍 检测正确的特征维度...")
    
    # 创建特征提取器
    extractor = MultiFeatureExtractor()
    
    # 找一个测试文件
    test_files = list(Path("Data/v2/hungry").glob("*.wav"))
    if not test_files:
        print("❌ 找不到测试文件")
        return None
    
    test_file = test_files[0]
    print(f"使用测试文件: {test_file.name}")
    
    try:
        # 提取特征
        features = extractor.extract_features(test_file, augment=False)
        if features:
            feature_dim = len(features[0])
            print(f"✅ 检测到特征维度: {feature_dim}")
            return feature_dim
        else:
            print("❌ 特征提取失败")
            return None
    except Exception as e:
        print(f"❌ 特征提取错误: {e}")
        return None


def create_fixed_model():
    """创建修复后的模型"""
    print("🚀 创建修复后的方案2模型")
    print("=" * 60)
    
    # 获取正确的特征维度
    feature_dim = get_correct_feature_dimension()
    if feature_dim is None:
        print("❌ 无法确定特征维度")
        return None
    
    # 创建设备
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"使用设备: {device}")
    
    # 创建修复后的模型
    model = FixedEnsembleModel(input_dim=feature_dim, num_classes=5)
    model = model.to(device)
    
    print(f"模型参数数量: {sum(p.numel() for p in model.parameters()):,}")
    
    # 尝试加载原始权重（如果兼容的话）
    weights_path = "ensemble_training_results/best_ensemble_model.pth"
    if Path(weights_path).exists():
        print("📥 尝试加载原始权重...")
        try:
            original_weights = torch.load(weights_path, map_location=device)
            
            # 尝试加载兼容的权重
            model_state = model.state_dict()
            loaded_keys = []
            
            for key, value in original_weights.items():
                if key in model_state:
                    if model_state[key].shape == value.shape:
                        model_state[key] = value
                        loaded_keys.append(key)
                    else:
                        print(f"⚠️ 跳过维度不匹配的权重: {key}")
                        print(f"   期望: {model_state[key].shape}, 实际: {value.shape}")
            
            model.load_state_dict(model_state)
            print(f"✅ 成功加载 {len(loaded_keys)} 个兼容的权重参数")
            
        except Exception as e:
            print(f"⚠️ 权重加载失败，使用随机初始化: {e}")
    else:
        print("⚠️ 原始权重文件不存在，使用随机初始化")
    
    return model, device


def test_fixed_model(model, device):
    """测试修复后的模型"""
    print("\n🧪 测试修复后的模型...")
    
    # 创建特征提取器
    extractor = MultiFeatureExtractor()
    
    # 找测试文件
    test_files = list(Path("Data/v2/hungry").glob("*.wav"))[:5]
    
    model.eval()
    success_count = 0
    
    for test_file in test_files:
        try:
            # 提取特征
            features = extractor.extract_features(test_file, augment=False)
            if not features:
                print(f"⚠️ 特征提取失败: {test_file.name}")
                continue
            
            # 转换为tensor
            feature_tensor = torch.FloatTensor(features[0]).unsqueeze(0).to(device)
            
            # 预测
            with torch.no_grad():
                output = model(feature_tensor)
                predicted = torch.argmax(output, dim=1).item()
                
            print(f"✅ {test_file.name}: 预测类别 {predicted}")
            success_count += 1
            
        except Exception as e:
            print(f"❌ 预测失败 {test_file.name}: {e}")
    
    print(f"\n📊 测试结果: {success_count}/{len(test_files)} 个文件预测成功")
    return success_count == len(test_files)


def save_fixed_model(model):
    """保存修复后的模型"""
    print("\n💾 保存修复后的模型...")
    
    output_dir = Path("ensemble_training_results")
    output_dir.mkdir(exist_ok=True)
    
    # 保存完整模型
    fixed_model_path = output_dir / "fixed_ensemble_v2_model.pth"
    torch.save(model, fixed_model_path)
    print(f"✅ 完整模型已保存: {fixed_model_path}")
    
    # 保存权重
    fixed_weights_path = output_dir / "fixed_ensemble_v2_weights.pth"
    torch.save(model.state_dict(), fixed_weights_path)
    print(f"✅ 权重已保存: {fixed_weights_path}")
    
    return fixed_model_path


def main():
    """主函数"""
    print("🚀 完全修复方案2模型")
    print("=" * 60)
    
    # 创建修复后的模型
    result = create_fixed_model()
    if result is None:
        print("❌ 模型创建失败")
        return
    
    model, device = result
    
    # 测试模型
    if test_fixed_model(model, device):
        print("\n✅ 模型测试成功！")
        
        # 保存模型
        model_path = save_fixed_model(model)
        
        print(f"\n🎉 方案2模型修复完成！")
        print(f"修复后的模型已保存到: {model_path}")
        print("现在可以使用修复后的模型进行v2数据验证")
        
    else:
        print("\n❌ 模型测试失败，需要进一步调试")


if __name__ == "__main__":
    main()
